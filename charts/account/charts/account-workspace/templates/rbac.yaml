{{- if and .Values.global.account.workspace.serviceAccount .Values.global.account.workspace.serviceAccount.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "account.workspace.role.name" . }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["secrets"]
  resourceNames: [
    {{ include "account.postgresSecretName" . | quote }},
    {{ include "account.redisSecretName" . | quote }}
  ]
  verbs: ["get", "watch", "list"]
- apiGroups: [""]
  resources: ["configmaps"]
  resourceNames: [{{ include "account.workspace.configmap.name" . | quote }}]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "account.workspace.roleBinding.name" . }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: {{ include "account.workspace.serviceaccount.name" . }}
roleRef:
  kind: Role
  name: {{ include "account.workspace.role.name" . }}
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "account.workspace.serviceaccount.name" . }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
  {{- if .Values.global.account.workspace.serviceAccount.annotations }}
  annotations:
    {{- toYaml .Values.global.account.workspace.serviceAccount.annotations | nindent 4 }}
  {{- end }}
{{- end }}
