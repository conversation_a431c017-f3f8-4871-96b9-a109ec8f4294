{{- if .Values.global.account.persistantVolumeClaimCreate }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.global.account.persistantVolumeClaimName }}
  labels:
    {{- include "account.pvc.labels" . | nindent 4 }}
{{- if eq .Values.global.storage "efs" }}
  annotations:
    volume.beta.kubernetes.io/storage-class: {{ .Values.global.storageClassName }}
{{- end }}
spec:
  storageClassName: {{ .Values.global.storageClassName }}
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.global.account.persistantVolumeClaimSize | default "1Gi" }}
{{- end }}
