# Default values
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  domain: "ucm-dev.eu-west-1.aws.pmicloud.biz"
  product: "simulator"
  env: "dev"
  serviceMonitor:
    enabled: false
  # First login password will be changed during first login.
  init_admin_password: Corezoid111!
  #######  PostgreSQL  ########
  ## Supported version  from 13.5.*
  ## for RDS minimum instance - db.t3.medium / master user set "postgres"
  db:
    image: "postgres:13.7-alpine"
    imagePullPolicy: "IfNotPresent"
    # if internal true - create and use internal postgres container
    # if internal false - enable external db, like aws rds
    internal: false
    ## secret configuration for postgresql
    create: true
    ## pvc name
    persistantVolumeClaimName: "postgresql-pvc-sa"
    secret:
      ## true - secret will be created automaticaly with provided values
      ## false - secret should be created manualy
      create: true
      ## secret name
      name: "postgresql-secret"
      ## for init db and roles dbsuperuser and dbuser cannot be changed (because used as plain text in sql)
      dbsuperuser: "postgres"
      ## password - for dbsuperuser
      dbsuperuserpwd: ""
      data:
        dbhost: "${db_host}"
        dbport: "5432"
        dbuser: "${db_username}"
        dbpwd: "${db_password}"
    # set false to disable if you don't want to use PGBouncer recommended - true
    bouncer: false
    bouncer_port: 5432
    bouncer_minReplicas: 1
    bouncer_maxReplicas: 6
    bouncer_resources:
      limits:
        cpu: "500m"
        memory: "500Mi"
      requests:
        cpu: "100m"
        memory: "100Mi"
    bouncer_log_level: debug
    # Maximum number of client connections allowed.
    maxclientconn: "100000"
    # How many server connections to allow per user/database pair. Can be overridden in the per-database configuration.
    default_pool_size: "5000"
    # Server is released back to pool after transaction finishes. default: session, see man https://wiki.postgresql.org/wiki/PgBouncer
    # transaction | session
    default_pool_mode: transaction
  ## NO CLUSTER Mode!
  redis:
    # if internal true - create and use internal k8s redis container
    # if internal false - enable external redis, like aws elasticache (Engine Version Compatibility: 3.2.10)
    internal: false
    ## pvc name if it already exist or was created manualy
    persistantVolumeClaimName: "redis-pvc-sa"
    ## secret configuration for redis
    sentinel:
      enable: false
      master_name: "mymaster"
    secret:
      ## true - secret will be created automatically with provided values
      ## false - secret should be created manually
      create: true
      name: "redis-secret"
      # you can specify different servers for redis for cache and PubSub  - or specify the same server in variables
      data:
        host: "${redis_host}"
        port: "6380"
        password: ""
  ###########################################
  ######## Settings for filesystems #########
  # Define global storage class: efs / nfs / manual
  storage: efs
  # Define global storageClass name
  storageClassName: ""
  persistantVolumeClaimCreate: true
  ######## Settings AWS EFS filesystem   ########
  efs:
    awsRegion: "eu-west-1"
    efsFileSystemId: "fs-0ba08cd9420f69eb8"
    ## set true if you choose storage: efs
    enabled: true
  ######## Settings NFS filesystem   ########
  nfs:
    ## set true if you choose storage: nfs
    enabled: false
  imageRegistry: "hub.corezoid.com"
  repotype: "public"
  imageInit:
    repository: hub.corezoid.com/hub.docker.com/library/alpine
    pullPolicy: IfNotPresent
    tag: "3.21"
#   imagePullSecrets:
#     name: develop-middleware-registry-secret
  deploymentStrategy:
    type: RollingUpdate
  networkPolicy:
    enabled: false
    monitoring:
      # Selector for prometheus namespace
      namespaceSelector:
        name: monitoring
      # Selector for prometheus deploymnets
      podSelector:
        release: prometheus-stack
  nameOverride: ""
  fullnameOverride: ""
  ingress:
    className: nginx
    annotations:
      kubernetes.io/ingress.class: "nginx"
  account:
    enabled: true
    type: in_house
    pullPolicy: IfNotPresent
    subDomain: "crzd-account-dev"
    port: 9080
    persistantVolumeClaimName: "workspace-pvc"
    persistantVolumeClaimCreate: false
    persistantVolumeClaimShareDir: "avatars"
    content_security_policy:
      urls:
        - https://aadcdn.msauth.net
    email_confirm: true
    autoscaling:
      enabled: true
      minReplicas: 1
      maxReplicas: 4
    resources:
      limits:
        cpu: 0
        memory: 500Mi
      requests:
        cpu: 100m
        memory: 200Mi
    service:
      type: ClusterIP
    frontend:
      enabled: true
      port: 80
      # Specifies whether a service account should be created
      serviceAccount:
        create: true
        # You can provide your serviceAccount name to use, also set create to false
        # name: ""
        annotations: {}
      autoscaling:
        enabled: true
        minReplicas: 1
        maxReplicas: 4
        targetCPUUtilizationPercentage: 60
    db:
      secret:
        ## true - secret will be created automaticaly with provided values
        ## false - secret should be created manualy
        create: true
        ## secret name
        name: "postgresql-secret"
        ## for init db and roles dbsuperuser and dbuser cannot be changed (because used as plain text in sql)
        dbsuperuser: "postgres"
        ## password - for dbsuperuser
        dbsuperuserpwd: ""
        data:
          dbhost: "${db_host}"
          dbport: "5432"
          dbuser: "${db_username}"
          dbpwd: "${db_password}"
    redis:
      # if internal true - create and use internal k8s redis container
      # if internal false - enable external redis, like aws elasticache (Engine Version Compatibility: 3.2.10)
      internal: false
      ## pvc name if it already exist or was created manualy
      persistantVolumeClaimName: "redis-pvc-sa"
      ## secret configuration for redis
      sentinel:
        enable: false
        master_name: "mymaster"
      secret:
        ## true - secret will be created automatically with provided values
        ## false - secret should be created manually
        create: true
        name: "redis-secret"
        # you can specify different servers for redis for cache and PubSub  - or specify the same server in variables
        data:
          host: "${redis_host}"
          port: "6380"
          password: ""
    config:
#      superadmin_url: https://superadmin-dev.corezoid.com
      webhook:
        url: https://crzd-dev.ucm-dev.eu-west-1.aws.pmicloud.biz/api/2/json/public/353/cbef3a0b96dd11249f2dc3e82b9bca024f98de73
        # tls:
        #   insecure_skip_verify: true
      secret: "${account_token}"
      admin_user:
        login: <EMAIL>
        password: admin111!
      access_token_expire_time: 3600
      refresh_token_expire_time: *********
      capi_password_block_lifetime_secs: 7776000
      allow_authorizations: true
      logging:
        level: debug
      login_pass_auth:
        password_salt: fie4FahsohlieKi2shoogahluwahKiur
        password_salt_algorithm: sha
      email_confirm: true
      # single_auth_provider_mode: apple
      resource_dir: /avatars
#      license:
#        license_server_secret: hBChLTcb4FGU39yBRoe5M2EkStD7i1b2EUvdnFUatnCa8IWZbs
#        license_server_url: https://superadmin-pre.corezoid.com/license/api/2/44485
      captcha:
        secret: 6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
        key: 6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
        verify_url: https://www.google.com/recaptcha/api/siteverify
        disabled: true
      forbidden_domains_re:
        - ^example\.com$
        - ^yandex\.*$
        - \.by$
        - \.ru$
        - \.su$
        # - ^gmail\.com$
      auth_url: http://account-auth-service:8080
      single_account_disabled: true
      auth_providers:
        saml:
          azure:
            enabled: true
            name: Azure SAML
            icon: https://aadcdn.msauth.net/shared/1.0/content/images/favicon_a_eupayfgghqiai7k9sol6lg2.ico
            idp_signs_metadata: false
            idp_metadata_data: <?xml version="1.0" encoding="utf-8"?><EntityDescriptor ID="_13cd15c2-191c-471b-8c9e-a5816a66f73c" entityID="https://sts.windows.net/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/" xmlns="urn:oasis:names:tc:SAML:2.0:metadata"><Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256" /><Reference URI="#_13cd15c2-191c-471b-8c9e-a5816a66f73c"><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" /><DigestValue>N10t/yQ8YVIU6MJLT6/PUgIQUfmtzcni2mTPNDITiTs=</DigestValue></Reference></SignedInfo><SignatureValue>YRRmb7N18EDlhxHdbEG/6Y0Uk0RqjL4/cMq9tK9X0p6i0e8yIEdj1+VmhkmuFoQCU9XbkgI09CAQIlB81/JLBRzCcOwEofltlWpLsEYpsN7b9QUTtXk5GjLhkyGGQObYjB3j37JB+UlwygR0iNmGLY5q5CDU5RLcscSRVY5TZxoRUOQ3t8N211qmpNc/o9W4ik5C2rfE6wtJTskZvJeY28U3SNWh+4ZLFiBpg2YUwjBExmxEKUhQHaAM3mb8Zy8JSWIBr487fhQ43+3TJEJNo7r6eV3HDu+XJZYHtejuToKpJ3sa5aFWa9UGNicuDOHM+Si5voepAaYlgJeMfjiK1Q==</SignatureValue><ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:X509Data><ds:X509Certificate>MIIC8DCCAdigAwIBAgIQGq71Mw9WOK5B5/t9oHMr6TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAzMTgwNzM1NTdaFw0yODAzMTgwNzM1NThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz2SCKeegjtdobbUC0WN/HFDvgVliWnZ9MkGTSr24OUUT/pqy61gLQWPWBaiK6FTagOw6EVtxFrbsZOhODtrfgl6XlJdmX+TECqoKX7/Ob1wm8dTTKLMzA1MkPK7bcrPM8fNo0TMzNBdlYihjQPpLi/nzOs3j07wn6hYwgxclMRXzAYCo+sKNxvNE5KVfVXVAfM8KqP/jJnJnCwW9IcI/lw4B9BMUDLVkPVqQrvx868l7jGd9DQQMJ6Uj1HFKbJMChy1/AgPAoMlLkvmqIip0HUFzyhWVdXgxOJoUxw4LV8CCLNITxntzvCH8BuEsJkhEQpyWSdKU72/eWAjTuW+lbQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBdou143QCzaciAOlVaztdqjR2jZ7lryryZob2vktzg9nnKrptssCmv5iFr30i3jeMbqAcuIyDwsLLzdYeSgDkvbMnMKC0liZi6N8BeoRCP7YY/f6cp0751Kz+W8LbNR45iUTC2zL/Ju2QmnpndtHFtqeuP3ZKVRBkfzwke1VcC3ys63jQFHq3mG0kiQd0N+n1fqVOZjr9LW0ezQalNpCHjj9o/fTYGD7W1TqqbEPgZfKloMleyMLhnDqoq/BqNWCXApZgotrUyoaClYmaxfQI46w67Fum0ld77NvMaZZ7YEPh4476gGFvktzg3QMAGqNQ+LOtsCeSMTCKnTZqbzIAu</ds:X509Certificate></ds:X509Data></ds:KeyInfo></Signature><RoleDescriptor xsi:type="fed:SecurityTokenServiceType" protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706"><KeyDescriptor use="signing"><KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#"><X509Data><X509Certificate>MIIC8DCCAdigAwIBAgIQGq71Mw9WOK5B5/t9oHMr6TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAzMTgwNzM1NTdaFw0yODAzMTgwNzM1NThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz2SCKeegjtdobbUC0WN/HFDvgVliWnZ9MkGTSr24OUUT/pqy61gLQWPWBaiK6FTagOw6EVtxFrbsZOhODtrfgl6XlJdmX+TECqoKX7/Ob1wm8dTTKLMzA1MkPK7bcrPM8fNo0TMzNBdlYihjQPpLi/nzOs3j07wn6hYwgxclMRXzAYCo+sKNxvNE5KVfVXVAfM8KqP/jJnJnCwW9IcI/lw4B9BMUDLVkPVqQrvx868l7jGd9DQQMJ6Uj1HFKbJMChy1/AgPAoMlLkvmqIip0HUFzyhWVdXgxOJoUxw4LV8CCLNITxntzvCH8BuEsJkhEQpyWSdKU72/eWAjTuW+lbQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBdou143QCzaciAOlVaztdqjR2jZ7lryryZob2vktzg9nnKrptssCmv5iFr30i3jeMbqAcuIyDwsLLzdYeSgDkvbMnMKC0liZi6N8BeoRCP7YY/f6cp0751Kz+W8LbNR45iUTC2zL/Ju2QmnpndtHFtqeuP3ZKVRBkfzwke1VcC3ys63jQFHq3mG0kiQd0N+n1fqVOZjr9LW0ezQalNpCHjj9o/fTYGD7W1TqqbEPgZfKloMleyMLhnDqoq/BqNWCXApZgotrUyoaClYmaxfQI46w67Fum0ld77NvMaZZ7YEPh4476gGFvktzg3QMAGqNQ+LOtsCeSMTCKnTZqbzIAu</X509Certificate></X509Data></KeyInfo></KeyDescriptor><fed:ClaimTypesOffered><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Name</auth:DisplayName><auth:Description>The mutable display name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Subject</auth:DisplayName><auth:Description>An immutable, globally unique, non-reusable identifier of the user that is unique to the application for which a token is issued.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Given Name</auth:DisplayName><auth:Description>First name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Surname</auth:DisplayName><auth:Description>Last name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/displayname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Display Name</auth:DisplayName><auth:Description>Display name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/nickname" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Nick Name</auth:DisplayName><auth:Description>Nick name of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationinstant" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Authentication Instant</auth:DisplayName><auth:Description>The time (UTC) when the user is authenticated to Windows Azure Active Directory.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationmethod" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Authentication Method</auth:DisplayName><auth:Description>The method that Windows Azure Active Directory uses to authenticate users.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/objectidentifier" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>ObjectIdentifier</auth:DisplayName><auth:Description>Primary identifier for the user in the directory. Immutable, globally unique, non-reusable.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/tenantid" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>TenantId</auth:DisplayName><auth:Description>Identifier for the user's tenant.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/identityprovider" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>IdentityProvider</auth:DisplayName><auth:Description>Identity provider for the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Email</auth:DisplayName><auth:Description>Email address of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Groups</auth:DisplayName><auth:Description>Groups of the user.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/accesstoken" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>External Access Token</auth:DisplayName><auth:Description>Access token issued by external identity provider.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/expiration" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>External Access Token Expiration</auth:DisplayName><auth:Description>UTC expiration time of access token issued by external identity provider.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/openid2_id" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>External OpenID 2.0 Identifier</auth:DisplayName><auth:Description>OpenID 2.0 identifier issued by external identity provider.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/claims/groups.link" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>GroupsOverageClaim</auth:DisplayName><auth:Description>Issued when number of user's group claims exceeds return limit.</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/role" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>Role Claim</auth:DisplayName><auth:Description>Roles that the user or Service Principal is attached to</auth:Description></auth:ClaimType><auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/wids" xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706"><auth:DisplayName>RoleTemplate Id Claim</auth:DisplayName><auth:Description>Role template id of the Built-in Directory Roles that the user is a member of</auth:Description></auth:ClaimType></fed:ClaimTypesOffered><fed:SecurityTokenServiceEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/wsfed</wsa:Address></wsa:EndpointReference></fed:SecurityTokenServiceEndpoint><fed:PassiveRequestorEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/wsfed</wsa:Address></wsa:EndpointReference></fed:PassiveRequestorEndpoint></RoleDescriptor><RoleDescriptor xsi:type="fed:ApplicationServiceType" protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706"><KeyDescriptor use="signing"><KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#"><X509Data><X509Certificate>MIIC8DCCAdigAwIBAgIQGq71Mw9WOK5B5/t9oHMr6TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAzMTgwNzM1NTdaFw0yODAzMTgwNzM1NThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz2SCKeegjtdobbUC0WN/HFDvgVliWnZ9MkGTSr24OUUT/pqy61gLQWPWBaiK6FTagOw6EVtxFrbsZOhODtrfgl6XlJdmX+TECqoKX7/Ob1wm8dTTKLMzA1MkPK7bcrPM8fNo0TMzNBdlYihjQPpLi/nzOs3j07wn6hYwgxclMRXzAYCo+sKNxvNE5KVfVXVAfM8KqP/jJnJnCwW9IcI/lw4B9BMUDLVkPVqQrvx868l7jGd9DQQMJ6Uj1HFKbJMChy1/AgPAoMlLkvmqIip0HUFzyhWVdXgxOJoUxw4LV8CCLNITxntzvCH8BuEsJkhEQpyWSdKU72/eWAjTuW+lbQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBdou143QCzaciAOlVaztdqjR2jZ7lryryZob2vktzg9nnKrptssCmv5iFr30i3jeMbqAcuIyDwsLLzdYeSgDkvbMnMKC0liZi6N8BeoRCP7YY/f6cp0751Kz+W8LbNR45iUTC2zL/Ju2QmnpndtHFtqeuP3ZKVRBkfzwke1VcC3ys63jQFHq3mG0kiQd0N+n1fqVOZjr9LW0ezQalNpCHjj9o/fTYGD7W1TqqbEPgZfKloMleyMLhnDqoq/BqNWCXApZgotrUyoaClYmaxfQI46w67Fum0ld77NvMaZZ7YEPh4476gGFvktzg3QMAGqNQ+LOtsCeSMTCKnTZqbzIAu</X509Certificate></X509Data></KeyInfo></KeyDescriptor><fed:TargetScopes><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://sts.windows.net/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/</wsa:Address></wsa:EndpointReference></fed:TargetScopes><fed:ApplicationServiceEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/wsfed</wsa:Address></wsa:EndpointReference></fed:ApplicationServiceEndpoint><fed:PassiveRequestorEndpoint><wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing"><wsa:Address>https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/wsfed</wsa:Address></wsa:EndpointReference></fed:PassiveRequestorEndpoint></RoleDescriptor><IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol"><KeyDescriptor use="signing"><KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#"><X509Data><X509Certificate>MIIC8DCCAdigAwIBAgIQGq71Mw9WOK5B5/t9oHMr6TANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTAzMTgwNzM1NTdaFw0yODAzMTgwNzM1NThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz2SCKeegjtdobbUC0WN/HFDvgVliWnZ9MkGTSr24OUUT/pqy61gLQWPWBaiK6FTagOw6EVtxFrbsZOhODtrfgl6XlJdmX+TECqoKX7/Ob1wm8dTTKLMzA1MkPK7bcrPM8fNo0TMzNBdlYihjQPpLi/nzOs3j07wn6hYwgxclMRXzAYCo+sKNxvNE5KVfVXVAfM8KqP/jJnJnCwW9IcI/lw4B9BMUDLVkPVqQrvx868l7jGd9DQQMJ6Uj1HFKbJMChy1/AgPAoMlLkvmqIip0HUFzyhWVdXgxOJoUxw4LV8CCLNITxntzvCH8BuEsJkhEQpyWSdKU72/eWAjTuW+lbQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQBdou143QCzaciAOlVaztdqjR2jZ7lryryZob2vktzg9nnKrptssCmv5iFr30i3jeMbqAcuIyDwsLLzdYeSgDkvbMnMKC0liZi6N8BeoRCP7YY/f6cp0751Kz+W8LbNR45iUTC2zL/Ju2QmnpndtHFtqeuP3ZKVRBkfzwke1VcC3ys63jQFHq3mG0kiQd0N+n1fqVOZjr9LW0ezQalNpCHjj9o/fTYGD7W1TqqbEPgZfKloMleyMLhnDqoq/BqNWCXApZgotrUyoaClYmaxfQI46w67Fum0ld77NvMaZZ7YEPh4476gGFvktzg3QMAGqNQ+LOtsCeSMTCKnTZqbzIAu</X509Certificate></X509Data></KeyInfo></KeyDescriptor><SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/saml2" /><SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/saml2" /><SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://login.microsoftonline.com/8b86a65e-3c3a-4406-8ac3-19a6b5cc52bc/saml2" /></IDPSSODescriptor></EntityDescriptor>
            userinfo_map:
              login: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name
              email: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name
              name: http://schemas.microsoft.com/identity/claims/displayname
    affinity: {}

    workspace:
      port: 8080
      persistantVolumeClaimName: "workspace-pvc"
      persistantVolumeClaimCreate: false
      autoscaling:
        enabled: true
        minReplicas: 1
        maxReplicas: 4
        targetCPUUtilizationPercentage: 80
        targetMemoryUtilizationPercentage: 80
      resources:
        limits:
          memory: 600Mi
        requests:
          cpu: 200m
          memory: 400Mi
      # Specifies whether a service account should be created
      serviceAccount:
        create: true
        # You can provide your serviceAccount name to use, also set create to false
        # name: ""
        annotations: {}
    auth:
      port: 8080
      autoscaling:
        enabled: true
        minReplicas: 1
        maxReplicas: 4
        targetCPUUtilizationPercentage: 80
        targetMemoryUtilizationPercentage: 80
      resources:
        limits:
          memory: 400Mi
        requests:
          cpu: 100m
          memory: 400Mi
      # Specifies whether a service account should be created
      serviceAccount:
        create: true
        # You can provide your serviceAccount name to use, also set create to false
        # name: ""
        annotations: {}
      auth_providers_keys:
        apple: ""

