apiVersion: v1
kind: Service
metadata:
  name: {{ include "account.workspace.service.name" . }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.service.sessionAffinity }}
  sessionAffinity: {{ .Values.service.sessionAffinity }}
  {{- end }}
  selector:
    {{- include "account.workspace.labels" . | nindent 6 }}
  ports:
    - name: {{ include "account.workspace.portHttpName" . }}
      port: {{ include "account.workspace.portNumber" . }}
      targetPort: {{ include "account.workspace.portNumber" . }}
      protocol: {{ include "account.workspace.portProtocol" . }}
    {{- if .Values.global.account.workspace.portMetrics }}
    - name: metrics
      port: {{ include "account.workspace.portMetricsNumber" . }}
      targetPort: {{ include "account.workspace.portMetricsNumber" . }}
      protocol: {{ include "account.workspace.portProtocol" . }}
    {{- end }}
