{{- $global_data := .Values.global -}}
{{- $account_data := $global_data.account -}}
{{- $frontend_data := $account_data.frontend -}}
{{- $app_name := (include "module.frontend.name" . ) }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "account.frontend.deployment.name" . }}
  labels:
    {{- include "account.frontend.labels" . | nindent 4 }}
spec:
  {{- if .Values.global.account.frontend.autoscaling.enabled }}
  replicas: {{ .Values.global.account.frontend.autoscaling.minReplicas }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "account.frontend.labels" . | nindent 6 }}
{{- with .Values.global.deploymentStrategy }}
  strategy:
{{ toYaml . | trim | indent 4 }}
{{- end }}
{{- if eq .Values.global.deploymentStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    {{- end }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "account.frontend.labels" . | nindent 8 }}
    spec:
      {{- if .Values.global.imagePullSecrets }}
      imagePullSecrets:
      {{- range $pullSecret := .Values.global.imagePullSecrets }}
        - name: {{ $pullSecret }}
      {{- end }}
      {{- end }}
      serviceAccountName: {{- include "account.frontend.serviceaccount.name" . | nindent 8 }}
      securityContext:
        {{- toYaml .Values.global.account.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "module.frontend.name" . }}
          image: {{ include "account.frontend.imageUrl" . | quote }}
          imagePullPolicy: {{ .Values.global.imagePullPolicy | default "IfNotPresent" }}
          ports:
            - name: {{ include "account.frontend.portHttpName" . }}
              containerPort: {{ include "account.frontend.portNumber" . }}
              protocol: {{ include "account.frontend.portProtocol" . }}
          {{- with $frontend_data.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{ include "account.frontend.liveness" . | nindent 10 }}
          {{ include "account.frontend.readiness" . | nindent 10 }}
          volumeMounts:
            - name: config-nginx
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
              readOnly: true
            - name: config-site
              mountPath: /etc/nginx/conf.d/{{ .Values.appName }}.conf
              subPath: {{ .Values.appName }}.conf
              readOnly: true
            {{- if .Values.global.account.persistantVolumeClaimCreate }}
            - name: {{ .Values.appName }}-claim
              mountPath: /{{ $account_data.persistantVolumeClaimShareDir }}
            {{- end }}
      volumes:
        - name: config-nginx
          configMap:
            name: {{ include "account.frontend.configmap.name.nginx" . }}
        - name: config-site
          configMap:
            name: {{ include "account.frontend.configmap.name.site" . }}
        {{- if .Values.global.account.persistantVolumeClaimCreate }}
        - name: {{ .Values.appName }}-claim
          persistentVolumeClaim:
            claimName: {{ .Values.global.account.persistantVolumeClaimName }}
            readOnly: true
        {{- end }}
      {{- with .Values.global.account.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.account.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.account.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
