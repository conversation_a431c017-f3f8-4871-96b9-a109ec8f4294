{{- if and .Values.global.account.config.auth_providers .Values.global.account.config.auth_providers.saml}}
{{- if .Values.global.account.config.auth_providers.saml.google_saml}}
{{- if eq .Values.global.account.config.auth_providers.saml.google_saml.enabled true }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.global.account.config.auth_providers.saml.google_saml.secret_name | default "metadata-google-secret" }}
  labels:
    app: {{ .Values.global.product }}
    backend: "auth"
type: Opaque
data:
  google_metadata.xml: |
    {{ .Values.global.account.config.auth_providers.saml.google_saml.auth_metadata_content | b64enc }}
{{- end }}
{{- end }}
{{- end }}
