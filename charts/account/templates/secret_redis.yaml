{{- $sa_secret_redis := "" }}

{{- if .Values.global.account.redis }}
{{- $sa_secret_redis = .Values.global.account.redis.secret }}
{{- else if .Values.global.redis }}
{{- $sa_secret_redis = .Values.global.redis.secret }}
{{- else }}
{{- $sa_secret_redis = nil }}
{{- end }}

{{- if $sa_secret_redis.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "account.redisSecretName" . }}
  labels:
    app: {{ .Values.global.product }}
    backend: "redis"
type: Opaque
data:
  {{- range $key, $value := $sa_secret_redis.data }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}
{{- end }}
