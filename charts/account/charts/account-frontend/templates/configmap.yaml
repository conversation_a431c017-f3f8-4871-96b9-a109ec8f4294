{{- $global_data := .Values.global }}
{{- $account_data := $global_data.account }}
{{- $oneApp := default false .Values.global.account.config.one_app }}

{{- define "common.security.headers" -}}
        add_header Strict-Transport-Security "max-age=********; includeSubDomains";
        add_header X-Content-Type-Options nosniff;
        {{- if .Values.global.account.frontend.disable_sameorigin }}
        #add_header X-Frame-Options SAMEORIGIN;
        {{- else }}
        add_header X-Frame-Options SAMEORIGIN;
        {{- end }}
        add_header X-XSS-Protection "1; mode=block";
        add_header 'Referrer-Policy' 'origin-when-cross-origin';
        add_header Permissions-Policy "geolocation=(), camera=()";
{{- end -}}

{{- define "common.csp.policy" -}}
        add_header Content-Security-Policy "default-src 'self' blob: 'unsafe-inline' 'unsafe-eval' data: {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }} https://unpkg.com wss://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }} https://*.control.events https://img.icons8.com/ https://www.google-analytics.com https://fonts.gstatic.com https://www.gstatic.com https://www.googletagmanager.com https://*.{{ .Values.global.domain }} https://github.com https://www.facebook.com https://appleid.apple.com https://*.ngrok-free.app https://*.google.com https://stats.g.doubleclick.net https://fonts.googleapis.com https://*.google.com.ua https://*.shopify.com https://shopify.com https://*.googleusercontent.com https://*.simulator.company; frame-ancestors 'self' https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }} https://*.control.events https://*.simulator.company {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }};";
{{- end -}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "account.frontend.configmap.name.nginx" . }}
  labels:
    {{- include "account.frontend.labels" . | nindent 4 }}
data:
  nginx.conf: |
    user  nginx;
    worker_processes 1;
    error_log /dev/stdout;
    pid        /var/run/nginx.pid;

    load_module /etc/nginx/modules/ngx_http_image_filter_module.so;

    events {
      worker_connections  32000;
      multi_accept on;
    }

    http {
      client_max_body_size 25m;
      server_names_hash_bucket_size 128;
      include       /etc/nginx/mime.types;
      default_type  application/octet-stream;
      log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
      log_format up_log '$server_addr\t$server_port\t$http_x_forwarded_for\t$time_iso8601\tforward-to $upstream_addr\t$request\t$status\t$body_bytes_sent\t$request_time\t$upstream_response_time\t$http_referer\t$http_user_agent\t$host';
      access_log /dev/stdout up_log;

      sendfile        on;
      tcp_nopush     on;

      {{- if .Values.global.simulator }}
      map $http_origin $cors_origin {
        default "";
        "https://{{ .Values.global.simulator.subDomain }}.{{ .Values.global.simulator.mainDomian }}" "https://{{ .Values.global.simulator.subDomain }}.{{ .Values.global.simulator.mainDomian }}";
        {{- if .Values.global.account.access_control_allow_origin }}
        {{- range .Values.global.account.access_control_allow_origin.urls }}
        "{{ . }}" "{{ . }}";
        {{- end }}
        {{- end }}
      }
      {{- end }}

      server_tokens   off;
      # disable buffering
      proxy_buffering off;
      proxy_max_temp_file_size 0;
      keepalive_timeout  65;
      gzip on;
      gzip_types text/css application/x-javascript text/xml application/xml application/xml+rss text/plain application/javascript text/javascript application/json image/png image/svg+xml;
      gzip_proxied any;

      include /etc/nginx/conf.d/*.conf;
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "account.frontend.configmap.name.site" . }}
  labels:
    {{- include "account.frontend.labels" . | nindent 4 }}
data:
  {{ .Values.appName }}.conf: |
    server {
      listen {{ include "account.frontend.portNumber" . }};
      server_name {{ .Values.global.account.subDomain }}.{{ .Values.global.domain }};

      access_log /dev/stdout up_log;
      error_log  /dev/stdout;

      root /ebsmnt/www/mw-auth;
      set $avatars_local    /{{ .Values.global.account.persistantVolumeClaimShareDir }};

      add_header Strict-Transport-Security "max-age=********; includeSubDomains";
      add_header X-Content-Type-Options nosniff;
      {{- if .Values.global.account.frontend.disable_sameorigin }}
      #add_header X-Frame-Options SAMEORIGIN;
      {{- else }}
      add_header X-Frame-Options SAMEORIGIN;
      {{- end }}
      add_header X-XSS-Protection "1; mode=block";
      add_header 'Referrer-Policy' 'origin-when-cross-origin';
      add_header Permissions-Policy "geolocation=(), camera=()";
      add_header Content-Security-Policy "default-src 'self' blob: 'unsafe-inline' 'unsafe-eval' data: {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }} https://unpkg.com wss://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }}  https://*.control.events https://img.icons8.com/ https://www.google-analytics.com https://fonts.gstatic.com https://www.googletagmanager.com https://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://github.com https://www.facebook.com https://appleid.apple.com https://*.ngrok-free.app https://*.google.com https://stats.g.doubleclick.net https://fonts.googleapis.com https://*.google.com.ua https://*.shopify.com https://shopify.com https://*.googleusercontent.com https://*.simulator.company; frame-ancestors 'self' https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }} https://*.control.events https://*.simulator.company {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }};";

      location @redirect {
        return 302 /avatars/54x54/0.jpg;
      }

      location /avatars {
        add_header      Content-Type image/jpeg;
        error_page 404 415 = @redirect;
        alias $avatars_local;

        add_header Strict-Transport-Security "max-age=********; includeSubDomains";
        add_header X-Content-Type-Options nosniff;
        {{- if .Values.global.account.frontend.disable_sameorigin }}
        #add_header X-Frame-Options SAMEORIGIN;
        {{- else }}
        add_header X-Frame-Options SAMEORIGIN;
        {{- end }}
        add_header X-XSS-Protection "1; mode=block";
        add_header 'Referrer-Policy' 'origin-when-cross-origin';
        add_header Permissions-Policy "geolocation=(), camera=()";
        {{- if .Values.global.simulator }}
        {{- if .Values.global.account.access_control_allow_origin }}
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        {{- else }}
        add_header 'Access-Control-Allow-Origin' 'https://{{ .Values.global.simulator.subDomain}}.{{ .Values.global.simulator.mainDomian }}';
        {{- end }}
        {{- end }}
        add_header Content-Security-Policy "default-src 'self' blob: 'unsafe-inline' 'unsafe-eval' data: {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }} https://unpkg.com wss://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }}  https://*.control.events https://img.icons8.com/ https://www.google-analytics.com https://fonts.gstatic.com https://www.googletagmanager.com https://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://github.com https://www.facebook.com https://appleid.apple.com https://*.ngrok-free.app https://*.google.com https://stats.g.doubleclick.net https://fonts.googleapis.com https://*.google.com.ua https://*.shopify.com https://shopify.com https://*.googleusercontent.com https://*.simulator.company; frame-ancestors 'self' https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }} https://*.control.events https://*.simulator.company {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }};";
      }

      location ~ ^/avatars/(.+)x(.+)/(.+) {
        set                         $width  $1;
        set                         $height $2;

        proxy_http_version 1.1;
        add_header      Content-Type image/jpeg;
        error_page 404 415 = @redirect;
        alias $avatars_local/$3;

        expires 1d;
        image_filter                crop  $width  $height;
        image_filter_buffer 20M;
        image_filter_jpeg_quality 75; # Desired JPG quality

        add_header Strict-Transport-Security "max-age=********; includeSubDomains";
        add_header X-Content-Type-Options nosniff;
        {{- if .Values.global.account.frontend.disable_sameorigin }}
        #add_header X-Frame-Options SAMEORIGIN;
        {{- else }}
        add_header X-Frame-Options SAMEORIGIN;
        {{- end }}
        add_header X-XSS-Protection "1; mode=block";
        add_header 'Referrer-Policy' 'origin-when-cross-origin';
        add_header Permissions-Policy "geolocation=(), camera=()";
        {{- if .Values.global.simulator }}
        {{- if .Values.global.account.access_control_allow_origin }}
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        {{- else }}
        add_header 'Access-Control-Allow-Origin' 'https://{{ .Values.global.simulator.subDomain}}.{{ .Values.global.simulator.mainDomian }}';
        {{- end }}
        {{- end }}
        add_header Content-Security-Policy "default-src 'self' blob: 'unsafe-inline' 'unsafe-eval' data: {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }} https://unpkg.com wss://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }}  https://*.control.events https://img.icons8.com/ https://www.google-analytics.com https://fonts.gstatic.com https://www.googletagmanager.com https://*.{{ .Values.global.env }}.{{ .Values.global.domain }} https://github.com https://www.facebook.com https://appleid.apple.com https://*.ngrok-free.app https://*.google.com https://stats.g.doubleclick.net https://fonts.googleapis.com https://*.google.com.ua https://*.shopify.com https://shopify.com https://*.googleusercontent.com https://*.simulator.company; frame-ancestors 'self' https://{{ .Values.global.account.subDomain}}.{{ .Values.global.domain }} https://*.control.events https://*.simulator.company {{- range .Values.global.account.content_security_policy.urls }} {{.}}{{- end }};";
      }

      location / {
        {{ include "common.security.headers" . }}
        {{ include "common.csp.policy" . }}
        try_files $uri /index.html;
      }

      {{- if .Values.global.account.config.one_app }}
      location ~ ^/(client|public|face|webhook|api|auth|oauth2|download|pmi_lms) {
        proxy_pass http://{{ include "account.workspace.service.name" . }}:{{ .Values.global.account.workspace.port | default 80}};
        add_header Cache-Control "no-cache, must-revalidate";
        {{ include "common.security.headers" . }}
        {{ include "common.csp.policy" . }}
      }
      {{- else }}
      location ~ ^/(api|auth|oauth2|download|pmi_lms) {
        proxy_pass http://{{ include "account.auth.service.name" . }}:{{ .Values.global.account.auth.port | default 80}};
        add_header Cache-Control "no-cache, must-revalidate";
        {{ include "common.security.headers" . }}
        {{ include "common.csp.policy" . }}
      }

      location ~ ^/(client|public|face|webhook) {
        proxy_pass http://{{ include "account.workspace.service.name" . }}:{{ .Values.global.account.workspace.port | default 80}};
        add_header Cache-Control "no-cache, must-revalidate";
      }
      {{- end }}
      
      location /liveness-proxy {
        proxy_pass http://{{ include "account.workspace.service.name" . }}:{{ .Values.global.account.workspace.port | default 80}}/liveness;
        add_header Cache-Control "no-cache, must-revalidate";
        {{ include "common.security.headers" . }}
        {{ include "common.csp.policy" . }}
      }
    }
