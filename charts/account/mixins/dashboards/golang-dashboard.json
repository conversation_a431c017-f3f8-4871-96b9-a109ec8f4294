{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "A quickstart to setup the Prometheus Go runtime exporter with preconfigured dashboards, alerting rules, and recording rules.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 14061, "graphTooltip": 0, "id": 51, "links": [{"asDropdown": true, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["account"], "targetBlank": false, "title": "Account dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average total bytes of memory reserved across all process instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "avg by(job)(go_memstats_sys_bytes{namespace=\"$namespace\",job=~\"$job\",instance=~\"$instance\"})", "range": true, "refId": "A"}], "title": "Total Reserved Memory", "type": "timeseries"}, {"datasource": {}, "description": "Average stack memory usage across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job) (go_memstats_stack_sys_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: stack inuse (avg)", "range": true, "refId": "A"}], "title": "Stack Memory Use", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average memory reservations by the runtime, not for stack or heap, across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_mspan_sys_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{instance}}: mspan (avg)", "range": true, "refId": "B"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_mcache_sys_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{instance}}: mcache (avg)", "range": true, "refId": "D"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_buck_hash_sys_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{instance}}: buck hash (avg)", "range": true, "refId": "E"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_gc_sys_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: gc (avg)", "range": true, "refId": "F"}], "title": "Other Memory Reservations", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average memory reserved, and actually in use, by the heap, across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_heap_sys_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: heap reserved (avg)", "range": true, "refId": "B"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_heap_inuse_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: heap in use (avg)", "range": true, "refId": "A"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_heap_alloc_bytes{job=~\"tns_app\",instance=~\".*\"})", "interval": "", "legendFormat": "{{job}}: heap alloc (avg)", "range": true, "refId": "C"}, {"datasource": {"uid": "$datasource"}, "expr": "avg by (job)(go_memstats_heap_idle_bytes{job=~\"tns_app\",instance=~\".*\"})", "interval": "", "legendFormat": "{{job}}: heap idle (avg)", "refId": "D"}, {"datasource": {"uid": "$datasource"}, "expr": "avg by (job)(go_memstats_heap_released_bytes{job=~\"tns_app\",instance=~\".*\"})", "interval": "", "legendFormat": "{{job}}: heap released (avg)", "refId": "E"}], "title": "Heap Memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average allocation rate in bytes per second, across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 4, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(rate(go_memstats_alloc_bytes_total{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{job}}: bytes malloced/s (avg)", "range": true, "refId": "A"}], "title": "Allocation Rate, Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average rate of heap object allocation, across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "rate(go_memstats_mallocs_total{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"}[$__rate_interval])", "interval": "", "legendFormat": "{{job}}: obj mallocs/s (avg)", "range": true, "refId": "A"}], "title": "Heap Object Allocation Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average number of live memory objects across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by(job)(go_memstats_mallocs_total{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"} - go_memstats_frees_total{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: object count (avg)", "range": true, "refId": "A"}], "title": "Number of Live Objects", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "Average number of goroutines across instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_goroutines{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: goroutine count (avg)", "range": true, "refId": "A"}], "title": "Goroutines", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_gc_duration_seconds{quantile=\"0\", namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: min gc time (avg)", "range": true, "refId": "A"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_gc_duration_seconds{quantile=\"1\", namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}}: max gc time (avg)", "range": true, "refId": "B"}], "title": "GC min & max duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "The number used bytes at which the runtime plans to perform the next GC, averaged across all instances of a job.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.4.0", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "avg by (job)(go_memstats_next_gc_bytes{namespace=\"$namespace\", job=~\"$job\", instance=~\"$instance\"})", "interval": "", "legendFormat": "{{job}} next gc bytes (avg)", "range": true, "refId": "A"}], "title": "Next GC, Bytes", "type": "timeseries"}], "refresh": "10s", "revision": 1, "schemaVersion": 39, "tags": ["go", "golang", "account"], "templating": {"list": [{"current": {"selected": false, "text": "sa-prod", "value": "sa-prod"}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(go_info{namespace=~\"sa-prod|account-dev|account-pre\"},namespace)", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"qryType": 1, "query": "label_values(go_info{namespace=~\"sa-prod|account-dev|account-pre\"},namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "Mimir", "value": "$datasource"}, "hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "account-workspace-service", "value": "account-workspace-service"}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(go_info{namespace=\"$namespace\"},job)", "description": "job", "hide": 0, "includeAll": true, "label": "job", "multi": false, "name": "job", "options": [], "query": {"qryType": 1, "query": "label_values(go_info{namespace=\"$namespace\"},job)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(go_info{namespace=\"$namespace\"},instance)", "hide": 0, "includeAll": true, "label": "instance", "multi": false, "name": "instance", "options": [], "query": {"qryType": 1, "query": "label_values(go_info{namespace=\"$namespace\"},instance)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Account <PERSON>", "uid": "account_golang", "version": 10, "weekStart": ""}