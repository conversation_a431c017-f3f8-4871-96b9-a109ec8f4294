# Configure Kubernetes and Helm providers to use the EKS cluster
# provider "kubernetes" {
#   host                   = data.aws_eks_cluster.cluster.endpoint
#   cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
#   token                  = data.aws_eks_cluster_auth.cluster.token
# }
#
# provider "helm" {
#   kubernetes {
#     host                   = data.aws_eks_cluster.cluster.endpoint
#     cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
#     token                  = data.aws_eks_cluster_auth.cluster.token
#   }
# }

#Create namespace
resource "kubernetes_namespace" "account-dev" {
  metadata {
    name = "account-dev"
    labels = {
      name        = "account-dev"
      environment = var.environment
      managed-by  = "terraform"
    }
  }

  depends_on = [module.eks, module.nginx_ingress]
}

# Deploy account application using local Helm chart
resource "helm_release" "account" {
  name       = "dbcall"
  namespace  = kubernetes_namespace.account-dev.metadata[0].name
  chart      = "${path.module}/charts/account"
  # Using local chart

  # Use the values from values-account.yaml file
  values = [
    templatefile("values-account.yaml.tpl",{
      host_app = var.host_app
      username_app = var.username_app
      password_app = var.password_app
    })
  ]

  # Additional configuration
  timeout         = 600
  cleanup_on_fail = true
  wait            = true

  depends_on = [
    kubernetes_namespace.account-dev,
    module.eks,
    module.nginx_ingress
  ]
}

# Output the helm release status
output "dbcall_release_status" {
  description = "The status of the dbcall helm release"
  value       = helm_release.account.status
}

# Output the helm release version
output "dbcall_release_version" {
  description = "The version of the dbcall helm release"
  value       = helm_release.account.version
}
