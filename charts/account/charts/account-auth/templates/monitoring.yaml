{{- if ne .Values.global.account.config.one_app true }}
{{- if .Values.global.serviceMonitor -}}
{{- if .Values.global.serviceMonitor.enabled -}}
apiVersion: {{ include "common.ServiceMonitor.apiVersion" . }}
kind: ServiceMonitor
metadata:
  name: {{ .Values.appName }}
  labels:
    {{- include "account.auth.labels" . | nindent 4 }}
    {{- include "common.ServiceMonitor.metadata.labes" . | nindent 4 }}
  annotations:
    description: "ServiceMonitor for account-auth metrics"
spec:
  selector:
    matchLabels:
      {{- include "account.auth.labels" . | nindent 6 }}
  endpoints:
  - path: {{ .Values.prometheusPath | default "/metrics" }}
    targetPort: {{ include "account.auth.portMetricsNumber" . }}
    interval: {{ .Values.prometheusInterval | default "15s" }}
    scrapeTimeout: {{ .Values.prometheusScrapeTimeout | default "10s" }}
    honorLabels: true
    metricRelabelings:
    {{- if .Values.prometheusMetricRelabelings }}
    {{- toYaml .Values.prometheusMetricRelabelings | nindent 4 }}
    {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
{{- end -}}
{{- end -}}
{{- end }}
