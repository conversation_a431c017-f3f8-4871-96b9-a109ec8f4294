{{- if .Values.global.account.config.one_app }}
{{- if .Values.global.serviceMonitor -}}
{{- if .Values.global.serviceMonitor.enabled -}}
apiVersion: {{ include "common.ServiceMonitor.apiVersion" . }}
kind: ServiceMonitor
metadata:
  name: {{ .Values.appName }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
    {{- include "common.ServiceMonitor.metadata.labes" . | nindent 4 }}
  annotations:
    description: "ServiceMonitor for account-workspace metrics"
spec:
  selector:
    matchLabels:
      {{- include "account.workspace.labels" . | nindent 6 }}
  endpoints:
  - path: {{ .Values.prometheus.path | default "/metrics" }}
    targetPort: {{ include "account.workspace.portMetricsNumber" . }}
    interval: {{ .Values.prometheus.interval | default "15s" }}
    scrapeTimeout: {{ .Values.prometheus.scrapeTimeout | default "10s" }}
    honorLabels: true
    metricRelabelings:
    {{- if .Values.prometheus.metricRelabelings }}
    {{- toYaml .Values.prometheus.metricRelabelings | nindent 4 }}
    {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
{{- end -}}
{{- end -}}
{{- end }}
