{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: sa-{{ .Values.global.env | default "prod" }}-frontend
  annotations: {{ toYaml .Values.global.ingress.annotations | nindent 4 }}
  labels:
    app: {{ .Values.global.product }}
    tier: {{ .Values.appName }}
    env: {{ .Values.global.env | default "prod" }}
spec:
  {{- if and .Values.global.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.global.ingress.className }}
  {{- end }}
  rules:
    - host: {{ .Values.global.account.subDomain }}.{{ .Values.global.domain }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ include "account.frontend.service.name" . }}
                port:
                  number: 80
