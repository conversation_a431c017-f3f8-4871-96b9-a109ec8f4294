appName: account-auth

postgresqlClientImage:
  registry: hub.corezoid.com/simulator
  repository: postgresql-client
  tag: "16.8"

image:
  repository: account-auth

configPath: "/opt/conf"
configName: "config.yml"
appAuthLivenessPath: "/liveness"
appAuthReadinessPath: "/readiness"

livenessProbe:
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

readinessProbe:
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

service:
  type: ClusterIP
  sessionAffinity: None
  annotations: {}
