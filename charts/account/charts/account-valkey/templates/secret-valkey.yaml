{{- if and (hasKey .Values.global.account "valkey") (not (empty .Values.global.account.valkey)) }}
{{- if .Values.global.account.valkey.use }}

{{- $account_secret_valkey := .Values.global.account.valkey.secret }}

apiVersion: v1
kind: Secret
metadata:
  name: {{ include "account.valkeySecretName" . }}
  labels:
    app: {{ .Values.global.product }}
    module: {{ .Chart.Name }}
    backend: "valkey"
type: Opaque
data:
  {{- range $key, $value := $account_secret_valkey.data }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}

{{- end }}
{{- end }}
