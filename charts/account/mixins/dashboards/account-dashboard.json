{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 50, "links": [{"asDropdown": true, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["account"], "targetBlank": false, "title": "Account dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 0}, "id": 2, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "editorMode": "builder", "expr": "{namespace=~\"$namespace\"} !~ `my_corezoid_0|http: named cookie not present|SpecifiedUserIsBlocked|AlreadyExist` | json | l = `E`", "legendFormat": "{{err}}", "queryType": "range", "refId": "A"}], "title": "Errors log", "type": "logs"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 0}, "id": 11, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "sum by(err) (sum_over_time(errors_total{namespace=\"$namespace\", err!~\"my_corezoid_0|http: named cookie not present|SpecifiedUserIsBlocked|AlreadyExist|\"}[$__interval]))", "format": "time_series", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{err}}", "range": true, "refId": "A", "useBackend": false}], "title": "Errors", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 7}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by (type) (increase(inbound_request_duration_seconds_count{namespace=\"$namespace\", path=~\"$path\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{type}} API", "range": true, "refId": "A", "useBackend": false}], "title": "Inbound RPM", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 9, "y": 7}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "expr": "avg(rate(inbound_request_duration_seconds_sum{namespace=\"$namespace\",path=~\"$path\"}[1m])) by (type) / avg(rate(inbound_request_duration_seconds_count{namespace=\"$namespace\",path=~\"$path\"}[1m])) by (type)", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{type}} API", "range": true, "refId": "A", "useBackend": false}], "title": "Inbound Latency (ms)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 7}, "id": 12, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "11.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum by (new_code) (\n  label_replace(\n    rate(inbound_request_duration_seconds_count{namespace=\"$namespace\",code=~'4..|5..'}[$__range]),\n    \"new_code\",\n    \"${1}xx\",\n    \"code\",\n    \"(^\\\\d).+\"\n  )\n) / \nscalar(sum(rate(inbound_request_duration_seconds_count{namespace=\"$namespace\"}[$__range]))) * 100", "instant": false, "legendFormat": "{{new_code}}", "range": true, "refId": "A"}], "title": "Inbound Error codes percentage", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 15}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by (type,url) (increase(outbound_request_duration_seconds_count{namespace=\"$namespace\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{type}}: {{url}}", "range": true, "refId": "A", "useBackend": false}], "title": "Outbound RPM", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 9, "y": 15}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "expr": "avg(rate(outbound_request_duration_seconds_sum{namespace=\"$namespace\"}[1m])) by (type,url) / avg(rate(outbound_request_duration_seconds_count{namespace=\"$namespace\"}[1m])) by (type,url)", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{type}}: {{url}}", "range": true, "refId": "A", "useBackend": false}], "title": "Outbound Latency (ms)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 15}, "id": 15, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "11.1.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum by (new_code) (\n  label_replace(\n    rate(outbound_request_duration_seconds_count{namespace=\"$namespace\",type=~\"event|notification\",code=~'3..|4..|5..'}[$__range]),\n    \"new_code\",\n    \"${1}xx\",\n    \"code\",\n    \"(^\\\\d).+\"\n  )\n) / \nscalar(sum(rate(outbound_request_duration_seconds_count{namespace=\"$namespace\",type=~\"event|notification\"}[$__range]))) * 100", "instant": false, "legendFormat": "{{new_code}}", "range": true, "refId": "A"}], "title": "Outbound Error codes percentage", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by (path) (increase(inbound_request_duration_seconds_count{namespace=\"$namespace\",path=~\"$path\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{method}}", "range": true, "refId": "A", "useBackend": false}], "title": "Inbound RPM per API", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 30}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"maxHeight": 600, "mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "disableTextWrap": false, "editorMode": "code", "expr": "avg(rate(inbound_request_duration_seconds_sum{namespace=\"$namespace\",path=~\"$path\"}[1m])) by (path) / avg(rate(inbound_request_duration_seconds_count{namespace=\"$namespace\",path=~\"$path\"}[1m])) by (path)", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{path}}", "range": true, "refId": "A", "useBackend": false}], "title": "Inbound Latency per API (ms)", "type": "timeseries"}], "refresh": "", "schemaVersion": 39, "tags": ["account"], "templating": {"list": [{"current": {"selected": false, "text": "sa-prod", "value": "sa-prod"}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(go_info,namespace)", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"qryType": 1, "query": "label_values(go_info,namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".+", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(inbound_request_duration_seconds_count,path)", "hide": 0, "includeAll": true, "label": "path", "multi": false, "name": "path", "options": [], "query": {"qryType": 1, "query": "label_values(inbound_request_duration_seconds_count,path)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "Mimir", "value": "$datasource"}, "hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Account App Dashboard", "uid": "account_app_dashboard", "version": 15, "weekStart": ""}