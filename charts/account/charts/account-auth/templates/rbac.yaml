{{- if ne .Values.global.account.config.one_app true }}
{{- if and .Values.global.account.auth.serviceAccount .Values.global.account.auth.serviceAccount.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "account.auth.role.name" . }}
  labels:
    {{- include "account.auth.labels" . | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["secrets"]
  resourceNames: [
    {{ include "account.postgresSecretName" . | quote }},
    {{ include "account.redisSecretName" . | quote }},
    {{ include "account.auth_provider_secret_name" . | quote }}
  ]
  verbs: ["get", "watch", "list"]
- apiGroups: [""]
  resources: ["configmaps"]
  resourceNames: [{{ include "account.auth.configmap.name" . | quote }}]
  verbs: ["get"]

---
# Binds the Role to the ServiceAccount
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "account.auth.roleBinding.name" . }}
  labels:
    {{- include "account.auth.labels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: {{ include "account.auth.serviceaccount.name" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "account.auth.role.name" . }}

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "account.auth.serviceaccount.name" . }}
  labels:
    {{- include "account.auth.labels" . | nindent 4 }}
  {{- if .Values.global.account.auth.serviceAccount.annotations }}
  annotations:
    {{- toYaml .Values.global.account.auth.serviceAccount.annotations | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
