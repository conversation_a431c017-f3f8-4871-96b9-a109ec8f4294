{{- if .Values.global.account.db }}
{{- if .Values.global.account.db.secret }}
{{- if .Values.global.account.db.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: sa-{{ .Values.global.account.db.secret.name}}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
    tier: "postgres"
type: Opaque
data:
  {{- range $key, $value := .Values.global.account.db.secret.data }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}
{{- end}}
{{- end}}
{{- end}}
