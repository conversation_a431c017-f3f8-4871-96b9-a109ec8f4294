{{- $currentScope := .Values.global.dashboards }}
{{- if and $currentScope.enabled .Values.global.serviceMonitor.enabled}}
{{ range $path, $_ :=  $.Files.Glob  "mixins/dashboards/*.json" }}
{{ $file := $path | base }}
{{ $name := print ($file | trimSuffix ".json")  "-dashboard" }}
{{- with $currentScope}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- with .labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{ $file }}: |-
{{ $.Files.Get $path | indent 4 }}
---
{{- end }}
{{ end }}
{{- end }}
