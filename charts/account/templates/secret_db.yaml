{{- $sa_secret_db := "" }}

{{- if .Values.global.account.db }}
{{- $sa_secret_db = .Values.global.account.db.secret }}
{{- else if .Values.global.db }}
{{- $sa_secret_db = .Values.global.db.secret }}
{{- else }}
{{- $sa_secret_db = nil }}
{{- end }}

{{- if $sa_secret_db.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "account.postgresSecretName" . }}
  labels:
    app: {{ .Values.global.product }}
    backend: "db"
type: Opaque
data:
  {{- range $key, $value := $sa_secret_db.data }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}
{{- end }}
