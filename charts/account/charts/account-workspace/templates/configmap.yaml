{{- $workspace_config := .Values.global.account.workspace }}
{{- $global_config := .Values.global.account.config }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "account.workspace.configmap.name" . }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
data:
  {{ .Values.configName }}: |
    env: {{ .Values.global.env | default "prod" }}
    type: {{ .Values.global.account.type | default "in_house" }}
    url: {{ .Values.global.account.protocol | default "https://" }}{{ .Values.global.account.subDomain }}.{{ .Values.global.domain }}
    listen:
      type: port
      bind_ip: 0.0.0.0
      port: {{ .Values.global.account.workspace.port }}
      metrics_port: {{ include "account.workspace.portMetricsNumber" . }}
    psql:
      host: ${POSTGRES_DBHOST}
      port: ${POSTGRES_DBPORT}
      user: ${POSTGRES_DBUSER}
      password: ${POSTGRES_DBPWD}
      database: ${DB_NAME}
    license_psql:
      host: ${POSTGRES_DBHOST}
      port: ${POSTGRES_DBPORT}
      user: ${POSTGRES_DBUSER}
      password: ${POSTGRES_DBPWD}
      database: {{ $workspace_config.license_db | default "settings" }}
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DB}
{{ $global_config | toYaml | trim | indent 4 }}
