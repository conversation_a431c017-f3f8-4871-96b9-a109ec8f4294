variable "environment" {
  description = "Environment name - e.g. dev/qa/prd/dev1/qa1 etc"
}

variable "environment_family" {
  description = "Environment Family - e.g. dev/qa/prd"
}

variable "name" {
  description = "name of environment"
}

variable "short_name" {
  description = "Short name of the account taken from account creation request"
}

## TFE variables
variable "tfe_org_name" {
  description = "Terraform Enterprise variable"
}

variable "tfe_host_name" {
  description = "Terraform Enterprise variable"
}

variable "role_name" {
  description = "AWS role name to assume"
  default     = "TLZOrgAdmin"
}

variable "account_id" {
  description = "The ID of the working account"
}

variable "owner" {
  description = "owner - this could be a tag"
}

variable "baseline_workspace_name" {
  description = "Baseline workspace to read the outputs"
}

### Tags
variable "tags" {
  description = "A map of tags to add to all resources"
  default     = {}
}

variable "region_primary" {
  default = "eu-west-1"
}

variable "region_secondary" {
  default = "eu-west-2"
}

### EC2 Key Pair
variable "public_key" {
  default = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDH6Oo0CigTvLPA9ef5BkR8CPjknh8qo23FJ43kOno0wjVHxU7tFJxcHXzcK6XcF7mLEH2Ff4ItpWBwRYc1bSBWBkKVzz6R7uCxboGMoCOYK5sTLE/AkrbokRyHaUV5JPLbd6ivu9MktIrEFFLg4pQb/dGWcADJOhLiBs+cTEJU91gcAL4FPBmgNHOTuhMOf0m2wgN72TNVr4MP9R7dkfbgpaKozSmivHqGce7moweEhw8ZPf1uHrK/AGhUZAjZz4SDGh5fYx87xyCfzvC3kFdykkcn2RxhldAi91tHIIjBirCKeYGXWcOrjwQzrzQksQOU/ExUjBPlaCqXXQ6tV9NJDW+10jMhGI6IKeNPUZkoZ9VtrENNKEzUG6MqTHrHfE+M9YYPL3//05lFlhl4u0XZ8jOQBbQMLj79PVIg/hW2hbh0B5psKbvZDmCdrKUeB5CXF59o3/UaqMp2Ql81u40n2ovLu59sqZeYqDY8UVpNbKBVXk7Yiz41cu+EwMRwPlp+Fs37GV7gmWaZg7LpxuKVy1rte4vEXMankT+1LFkTTdFeAz5n0igiYSfkqpx0EYcFPbmTVpfkgqPnE5/srjPAaXiXKNL8ziYc5SwpVC+AEbQ5Z2KotZtFNuUvQPTC3ygvXURrw0hcRFfVv1mMnmovMmSzTdg3g0mnmTMrZxEi7Q== vision@PMIKZALAL10887"
}
variable "public_key0" {
  default = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC4WZeGd8tXGwWJE3mRoqUSCBK34MmVlRBrmpcsbIlPpGQ6C1ymhwXlEgGpapzijz0cB0EErPOi/ExmJ0dz4TR1sTLAjA1qp0SL6N1KGNSJtD5yHLlS6JWxN6wgsxiYD8aYfOso3ecM8tfIrw+LfUie1aTEc2lsHX6R2NlwZIm3PuRcR00uer7HvXRAN9x74HUgsx1aixklIINisjhHPxBKX6MUEUWFHJVwAMgJDMQZEXP5n1owx+E2n+RZ+n285D9PJeHdY4JK3wzWqY//tFP777Gq79qmVon9ZkU9bFW4NXjAxHSenmennAnIzdOdEVhpfsPlwqPVcZ6Lch4JdjPioswYZXF/8be1TLX/pz8+uPvYvn2PNgnvKnvIJHluY+oIZLJqrR1Pp4BoT8TUFA1HcQyw8yji8TyTZEEfmuhO3jCouYG9TkpgQi24c2Pd/hctIW1ncy4xIhai6YyqHMQxYXvMSftCg0cctoBNlt78odkd2AYW2ZGyWdoptRN+8U0oiXuRwhfVUkDjRpg899Q4VTnabLi8A9JR6NMCLj+63j8HJggVBNBDON2IGd125VnLSeRSo8EYSDJXirFcZoNo1DVybp6z6bEwD35x4fUOX+BQpiCkwcBwMT34tlAug/AMK0ynePYF5HhbCgs68eGPlpnkBAAhN+DWjPYjwe+nBw== vshevche14@PMIKZALAL10378"
}

variable "public_key3" {
  default = "ucm_dev_machine_3"
}

variable "ssh_key" {
  default = "mw-dev"
}

variable "ssh_key4" {
  default = "public_key4"
}

locals {
  baseline_version = "v0.0.2"

  Backup_Plan       = "none"
  MaintenanceWindow = "FRI_21" // UTC

  ProductCode       = upper(var.short_name)
  Environment       = upper(var.environment)
  EnvironmentFamily = upper(var.environment_family)

  common_tags = {
    project                   = var.name
    stack_env                 = var.environment
    security_data_sensitivity = "protected"
    stack                     = var.name
    stack_version             = "0.1.0"
    stack_lifecycle           = "perm"

    ProductCode       = local.ProductCode
    Environment       = local.Environment
    EnvironmentFamily = local.EnvironmentFamily

    Backup_Plan       = local.Backup_Plan
    MaintenanceWindow = local.MaintenanceWindow
  }

  #
  # eks
  #

  eks_default_kms = data.terraform_remote_state.app-baseline.outputs.deep_default_kms
  eks_managed_node_groups = {
    default_node_group = {
      min_size                              = 2
      max_size                              = 4
      desired_size                          = 2
      create_launch_template                = true
      attach_cluster_primary_security_group = true
      ami_type                              = "AL2023_x86_64_STANDARD"
      instance_types                        = ["t3.medium"]
      network_interfaces                    = [{ associate_public_ip_address = false }]
      block_device_mappings = {
        xvda = {
          device_name = "/dev/xvda"
          ebs = {
            volume_size           = 50
            volume_type           = "gp3"
            encrypted             = true
            delete_on_termination = true
          }
        }
      }
      iam_role_additional_policies = {
        additional  = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
        additional2 = "arn:aws:iam::aws:policy/AmazonAPIGatewayInvokeFullAccess"
        additional3 = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
      }
      labels = local.common_tags
    }
  }
  eks_module_appstream      = data.terraform_remote_state.app-baseline.outputs.appstream_fleet_role_arn
  eks_module_region         = data.terraform_remote_state.app-baseline.outputs.region
  eks_module_vpc            = data.terraform_remote_state.app-baseline.outputs.vpc_id_primary
  eks_module_acm            = data.terraform_remote_state.app-baseline.outputs.acm
  pmi_private_ca_arn        = "arn:aws:acm-pca:eu-west-1:630707141366:certificate-authority/3d8642f1-8766-4155-9737-b04e609a6573"
  default_kms_key           = data.terraform_remote_state.app-baseline.outputs.deep_default_kms
  private_subnets_alb       = data.terraform_remote_state.app-baseline.outputs.private_subnets_primary
  route53_acn_pvt_zone_name = data.terraform_remote_state.app-baseline.outputs.route53_acn_pvt_zone_name

}

variable "webserver2" {
  type = map(any)
  default = {
    name          = "ucm_dev_machine"
    instance_type = "m5a.xlarge"

    platform                = "linux"
    os                      = "amazonlinux2"
    disable_api_termination = "true"

    root_volume_size = "32"
    root_volume_type = "gp3"
  }
}

#ebs volume device variable declaration - extra storage for EC2 instance
variable "ebs_volume_device" {
  description = "EBS volumes to be created that needs to be attached to the instance"
  type        = map(map(string))
  default = {
    sdc = {
      device_name           = "/dev/sdc",
      volume_size           = "250",
      volume_type           = "gp3",
      iops                  = "3000",
      delete_on_termination = "no"
    }
  }
}

### rds-webserver module
#variable "webserver_rds" {
#  type = map
#  default = {
#    "db_engine"               = "mysql"
#    "db_engine_version"       = "5.7"
#    "db_major_version"        = "5.7"
#    "db_instance_class"       = "db.t3.micro"
#    "db_port"                 = "3306"
#    "db_username"             = "devdb_admin"
#    "identifier"              = "dev-db"
#    "storage_size"		        = "20"
#    "storage_type"            = "gp2"
#    "db_name"                 = "web"
#  }
#}

#r53 custom domain variable
variable "app_domain" {
  default = "cdn-dev.ucm-dev.eu-west-1.aws.pmicloud.biz"
}

variable "static_page_domain" {
  default = " "
}

variable "redis_host" {
  description = "redis_host"
}

variable "account_token" {
  description = "account_token"
}

variable "db_host" {
  description = "db_host"
}

variable "db_username" {
  description = "db_host"
}

variable "db_password" {
  description = "db_host"
}

variable "account_skip" {
  description = "account_skip"
}

