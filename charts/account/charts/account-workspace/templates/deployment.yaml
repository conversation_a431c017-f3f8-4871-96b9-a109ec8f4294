{{- $global_data := .Values.global -}}
{{- $account_data := $global_data.account -}}
{{- $workspace_data := $account_data.workspace -}}
{{- $auth_data := $account_data.auth -}}
{{- $app_name := (include "module.workspace.name" . ) -}}
{{- $application_tag := .Values.global.account.workspace.tag | default .Chart.AppVersion -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "account.workspace.deployment.name" . }}
  labels:
    {{- include "account.workspace.labels" . | nindent 4 }}
spec:
{{- if $workspace_data.autoscaling }}
{{- if $workspace_data.autoscaling.enabled }}
  replicas: {{ $workspace_data.autoscaling.minReplicas }}
{{- end }}
{{- else }}
  replicas: {{ $workspace_data.replicas | default 1 }}
{{- end }}
  selector:
    matchLabels:
      {{- include "account.workspace.labels" . | nindent 6 }}
{{- with $global_data.deploymentStrategy }}
  strategy:
{{ toYaml . | trim | indent 4 }}
{{- end }}
{{- if eq $global_data.deploymentStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
{{- end }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "account.workspace.labels" . | nindent 8 }}
        app.kubernetes.io/name: {{ include "module.workspace.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/version: {{ $application_tag | quote }}
        app.kubernetes.io/component: workspace
        app.kubernetes.io/part-of: singlespace
    spec:
      {{- if .Values.global.imagePullSecrets }}
      imagePullSecrets:
      {{- if kindIs "slice" .Values.global.imagePullSecrets }}
      {{- range $pullSecret := .Values.global.imagePullSecrets }}
        - name: {{ $pullSecret }}
      {{- end }}
      {{- else if .Values.global.imagePullSecrets.name }}
        - name: {{ .Values.global.imagePullSecrets.name }}
      {{- end }}
      {{- end }}
      serviceAccountName: {{- include "account.workspace.serviceaccount.name" . | nindent 8 }}
      securityContext:
        {{- toYaml $account_data.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: {{ $workspace_data.terminationGracePeriodSeconds | default 60 }}
      initContainers:
        {{ include "AccountInitWaitDatabase" . | nindent 8 | trim }}
        {{ include "AccountInitWaitRedis" . | nindent 8 | trim }}
        {{- if and (hasKey $workspace_data "verify_signature") (eq $workspace_data.verify_signature true) }}
        {{ include "AccountInitVerifyImageSignature" (dict "Values" .Values "Chart" .Chart "Release" .Release "subchart" "workspace" "application_tag" $application_tag) | nindent 8 | trim }}
        {{- end }}

      containers:
        - name: {{ include "module.workspace.name" . }}
          securityContext:
            {{- toYaml $account_data.securityContext | nindent 14 }}
          image: {{ include "account.workspace.imageUrl" . | quote }}
          imagePullPolicy: {{ $account_data.pullPolicy }}
          env:
            - name: SS_CONFIG
              value: {{ .Values.configPath }}/{{ .Values.configName }}
            {{- if .Values.global.account.config.one_app }}
            {{- if $auth_data.auth_providers_keys }}
            {{- if $auth_data.auth_providers_keys.apple }}
            - name: AUTH_APPLE_SECRET_PATH
              value: "{{ .Values.configPath }}/apple"
            {{- end }}
            {{- end }}
            {{- end }}
          {{- include "account.container_envs_redis" . | trim | nindent 12 }}
          {{- include "account.container_envs_db" . | trim | nindent 12 }}
          ports:
            - name: {{ include "account.workspace.portHttpName" . }}
              containerPort: {{ include "account.workspace.portNumber" . }}
              protocol: {{ include "account.workspace.portProtocol" . }}
            - name: metrics
              containerPort: {{ include "account.workspace.portMetricsNumber" . }}
              protocol: {{ include "account.workspace.portProtocol" . }}
          {{- include "account.workspace.liveness" . | nindent 10 }}
          {{- include "account.workspace.readiness" . | nindent 10 }}
          resources:
          {{- if $workspace_data.resources }}
            {{- toYaml $workspace_data.resources | nindent 12 }}
          {{- else }}
            {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 10"]
          volumeMounts:
            - name: {{ $app_name }}-config-volume
              mountPath: {{ .Values.configPath }}/{{ .Values.configName }}
              subPath: config.yml
            {{- if $account_data.persistantVolumeClaimCreate }}
            - name: {{ $app_name }}-pvc
              mountPath: /{{ $account_data.persistantVolumeClaimShareDir }}
            {{- end }}
            {{- if .Values.global.account.config.one_app }}
            {{- if and $account_data.config.auth_providers $account_data.config.auth_providers.saml }}
            {{- if $account_data.config.auth_providers.saml.google_saml }}
            {{- if eq $account_data.config.auth_providers.saml.google_saml.enabled true }}
            - name: metadata-google
              mountPath: /opt/conf/google_metadata.xml
              subPath: google_metadata.xml
            {{- end }}
            {{- end }}
            {{- end }}
            {{- if $auth_data.auth_providers_keys }}
            {{- if $auth_data.auth_providers_keys.apple }}
            - name: auth-apple-secret
              mountPath: {{ .Values.configPath }}/apple
              subPath: apple
            {{- end }}
            {{- end }}
            {{- end }}
      volumes:
        - name: {{ $app_name }}-config-volume
          configMap:
            name: {{ include "account.workspace.configmap.name" . }}
        {{- if $account_data.persistantVolumeClaimCreate }}
        - name: {{ $app_name }}-pvc
          persistentVolumeClaim:
            claimName: {{ $account_data.persistantVolumeClaimName }}
            readOnly: false
        {{- end }}
        {{- if .Values.global.account.config.one_app }}
        {{- if and $account_data.config.auth_providers $account_data.config.auth_providers.saml }}
        {{- if $account_data.config.auth_providers.saml.google_saml }}
        {{- if eq $account_data.config.auth_providers.saml.google_saml.enabled true }}
        - name: metadata-google
          secret:
            secretName: {{ $account_data.config.auth_providers.saml.google_saml.secret_name | default "metadata-google-secret" }}
        {{- end }}
        {{- end }}
        {{- end }}
        {{- if $auth_data.auth_providers_keys }}
        {{- if $auth_data.auth_providers_keys.apple }}
        - name: auth-apple-secret
          secret:
            secretName: {{ include "account.auth_provider_secret_name" . }}
            items:
            - key: apple
              path: apple
        {{- end }}
        {{- end }}
        {{- end }}
      {{- with $account_data.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
      {{- if $account_data.affinity }}
        {{- toYaml $account_data.affinity | nindent 8 }}
      {{- else }}
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - {{ include "module.workspace.name" . }}
              topologyKey: kubernetes.io/hostname
      {{- end }}
      {{- with $account_data.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
